# Test info

- Name: SPO Health Check >> TC0101- Login and Check the Default Data Source Order
- Location: C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:34:11

# Error details

```
TimeoutError: locator.waitFor: Timeout 30000ms exceeded.
Call log:
  - waiting for getByRole('button', { name: 'All', exact: true }) to be visible
    - waiting for" https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kp…" navigation to finish...
    - navigated to "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kp…"

    at PlaywrightWorld.waitForElement (C:\workspace\Playwright_Automation_Framework\src\core\playwright-world.ts:335:19)
    at Object.handler (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:170:21)
    at StepDefinitionRegistry.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:334:23)
    at PlaywrightCucumberRunner.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:196:31)
    at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:231:20)
    at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9
```

# Page snapshot

```yaml
- link "Skip to main content":
  - /url: javascript:;
- banner:
  - button "App launcher": 
  - link "Organizational Logo":
    - /url: https://m365.cloud.microsoft?auth=2&home=1&from=ShellLogo&username=<EMAIL>&login_hint=<EMAIL>
    - img "Organizational Logo"
  - link "Go to SharePoint":
    - /url: https://kpmgindia365.sharepoint.com/_layouts/15/sharepoint.aspx?&login_hint=<EMAIL>
    - text: SharePoint
  - search:
    - combobox "Search box. Suggestions appear as you type": kpmg
  - button "Next steps to improve your site"
  - button "Provide feedback to Microsoft"
  - button "Settings"
  - button "Help"
  - button "Account manager for K, Jagannatha": K, Jagannatha
- navigation "App bar":
  - list:
    - listitem:
      - button "Global navigation"
    - listitem:
      - button "My sites"
    - listitem:
      - button "My news"
    - listitem:
      - button "My files"
    - listitem:
      - button "My lists"
    - listitem:
      - button "Create"
- region "Command bar":
  - menubar:
    - group
    - group:
      - menuitem "Published 6/2/2025"
      - menuitem "Edit Page": Edit
      - menuitem "Hide header and navigation"
- article:
  - main:
    - button "Exit search"
    - button "All Document Types"
    - button "Language"
    - button "Service, Function, Capabilities"
    - button "Industry, Sector, Market"
    - button "All Filters"
    - text: Sorry, there was an authentication issue loading results. Please reload to try again.
    - button "Reload the page"
- text: Published 6/2/2025
```

# Test source

```ts
  235 |       let locator: any;
  236 |       const firstPart = parts[0];
  237 |
  238 |       if (firstPart.startsWith('getByRole(')) {
  239 |         locator = this.parseGetByRole(firstPart);
  240 |       } else if (firstPart.startsWith('getByText(')) {
  241 |         locator = this.parseGetByText(firstPart);
  242 |       } else if (firstPart.startsWith('getByLabel(')) {
  243 |         locator = this.parseGetByLabel(firstPart);
  244 |       } else if (firstPart.startsWith('getByPlaceholder(')) {
  245 |         locator = this.parseGetByPlaceholder(firstPart);
  246 |       } else if (firstPart.startsWith('getByTestId(')) {
  247 |         locator = this.parseGetByTestId(firstPart);
  248 |       } else {
  249 |         locator = this.page.locator(firstPart);
  250 |       }
  251 |
  252 |       // Chain the remaining parts
  253 |       for (let i = 1; i < parts.length; i++) {
  254 |         const part = 'getBy' + parts[i];
  255 |
  256 |         if (part.startsWith('getByText(')) {
  257 |           const match = part.match(/getByText\('([^']+)'(?:,\s*({[^}]+}))?\)/);
  258 |           if (match) {
  259 |             const text = match[1];
  260 |             const optionsStr = match[2];
  261 |
  262 |             if (optionsStr) {
  263 |               try {
  264 |                 const options = eval(`(${optionsStr})`);
  265 |                 locator = locator.getByText(text, options);
  266 |               } catch {
  267 |                 locator = locator.getByText(text);
  268 |               }
  269 |             } else {
  270 |               locator = locator.getByText(text);
  271 |             }
  272 |           }
  273 |         } else if (part.startsWith('getByRole(')) {
  274 |           const match = part.match(/getByRole\('([^']+)'(?:,\s*({[^}]+}))?\)/);
  275 |           if (match) {
  276 |             const role = match[1];
  277 |             const optionsStr = match[2];
  278 |
  279 |             if (optionsStr) {
  280 |               try {
  281 |                 const options = eval(`(${optionsStr})`);
  282 |                 locator = locator.getByRole(role as any, options);
  283 |               } catch {
  284 |                 locator = locator.getByRole(role as any);
  285 |               }
  286 |             } else {
  287 |               locator = locator.getByRole(role as any);
  288 |             }
  289 |           }
  290 |         }
  291 |         // Add more chained locator types as needed
  292 |       }
  293 |
  294 |       return locator;
  295 |
  296 |     } catch (error) {
  297 |       this.logger.warn(`Failed to parse chained locator: ${selector}, falling back to simple locator`);
  298 |       return this.page.locator(selector);
  299 |     }
  300 |   }
  301 |
  302 |   /**
  303 |    * Click on element
  304 |    */
  305 |   async clickElement(selector: string): Promise<void> {
  306 |     this.logger.info(`🖱️ Clicking: ${selector}`);
  307 |     const element = this.parseLocator(selector);
  308 |     await element.click();
  309 |   }
  310 |
  311 |   /**
  312 |    * Fill input field
  313 |    */
  314 |   async fillField(selector: string, value: string): Promise<void> {
  315 |     this.logger.info(`⌨️ Filling ${selector} with: ${value}`);
  316 |     const element = this.parseLocator(selector);
  317 |     await element.fill(value);
  318 |   }
  319 |
  320 |   /**
  321 |    * Type in input field
  322 |    */
  323 |   async typeInField(selector: string, value: string): Promise<void> {
  324 |     this.logger.info(`⌨️ Typing ${value} in: ${selector}`);
  325 |     const element = this.parseLocator(selector);
  326 |     await element.type(value);
  327 |   }
  328 |
  329 |   /**
  330 |    * Wait for element to be visible
  331 |    */
  332 |   async waitForElement(selector: string, timeout: number = 30000): Promise<void> {
  333 |     this.logger.info(`⏳ Waiting for element: ${selector}`);
  334 |     const element = this.parseLocator(selector);
> 335 |     await element.waitFor({ state: 'visible', timeout });
      |                   ^ TimeoutError: locator.waitFor: Timeout 30000ms exceeded.
  336 |   }
  337 |
  338 |   /**
  339 |    * Check if element is visible
  340 |    */
  341 |   async isElementVisible(selector: string): Promise<boolean> {
  342 |     try {
  343 |       const element = this.parseLocator(selector);
  344 |       return await element.isVisible();
  345 |     } catch {
  346 |       return false;
  347 |     }
  348 |   }
  349 |
  350 |   /**
  351 |    * Get element text
  352 |    */
  353 |   async getElementText(selector: string): Promise<string> {
  354 |     const element = this.parseLocator(selector);
  355 |     return await element.textContent() || '';
  356 |   }
  357 |
  358 |   /**
  359 |    * Press keyboard key
  360 |    */
  361 |   async pressKey(key: string): Promise<void> {
  362 |     this.logger.info(`⌨️ Pressing key: ${key}`);
  363 |     await this.page.keyboard.press(key);
  364 |   }
  365 |
  366 |   /**
  367 |    * Scroll to element
  368 |    */
  369 |   async scrollToElement(selector: string): Promise<void> {
  370 |     this.logger.info(`📜 Scrolling to: ${selector}`);
  371 |     const element = this.parseLocator(selector);
  372 |     await element.scrollIntoViewIfNeeded();
  373 |   }
  374 |
  375 |   /**
  376 |    * Wait for specified time
  377 |    */
  378 |   async wait(milliseconds: number): Promise<void> {
  379 |     this.logger.info(`⏳ Waiting for ${milliseconds}ms`);
  380 |     await this.page.waitForTimeout(milliseconds);
  381 |   }
  382 |
  383 |   /**
  384 |    * Verify text is present on page
  385 |    */
  386 |   async verifyTextPresent(text: string): Promise<void> {
  387 |     this.logger.info(`🔍 Verifying page contains: ${text}`);
  388 |     const bodyLocator = this.page.locator('body');
  389 |     await expect(bodyLocator).toContainText(text);
  390 |   }
  391 |
  392 |   /**
  393 |    * Verify element is visible
  394 |    */
  395 |   async verifyElementVisible(selector: string): Promise<void> {
  396 |     this.logger.info(`👁️ Verifying element is visible: ${selector}`);
  397 |     const element = this.parseLocator(selector);
  398 |     await expect(element).toBeVisible();
  399 |   }
  400 |
  401 |   /**
  402 |    * Verify page title contains text
  403 |    */
  404 |   async verifyTitleContains(text: string): Promise<void> {
  405 |     this.logger.info(`📄 Verifying title contains: ${text}`);
  406 |     const title = await this.getTitle();
  407 |     if (!title.includes(text)) {
  408 |       throw new Error(`Expected title to contain "${text}", but got "${title}"`);
  409 |     }
  410 |   }
  411 |
  412 |   /**
  413 |    * Verify element contains text
  414 |    */
  415 |   async verifyElementContainsText(selector: string, text: string): Promise<void> {
  416 |     this.logger.info(`🔍 Verifying ${selector} contains: ${text}`);
  417 |     const element = this.parseLocator(selector);
  418 |     await expect(element).toContainText(text);
  419 |   }
  420 | }
  421 |
```