<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="78.15891400000001">
<testsuite name="cucumber-wrapper.spec.ts" timestamp="2025-06-05T07:30:05.802Z" hostname="chromium" tests="1" failures="1" skipped="0" time="72.659" errors="0">
<testcase name="SPO Health Check › TC0101- Login and Check the Default Data Source Order" classname="cucumber-wrapper.spec.ts" time="72.659">
<properties>
<property name="feature" value="SPO Health Check">
</property>
<property name="tags" value="@combined, @spo">
</property>
<property name="file" value="src\features\spo-health-check.feature">
</property>
</properties>
<failure message="cucumber-wrapper.spec.ts:34:11 TC0101- Login and Check the Default Data Source Order" type="FAILURE">
<![CDATA[  [chromium] › cucumber-wrapper.spec.ts:34:11 › SPO Health Check › TC0101- <PERSON><PERSON> and Check the Default Data Source Order 

    TimeoutError: locator.waitFor: Timeout 30000ms exceeded.
    Call log:
      - waiting for getByRole('button', { name: 'All', exact: true }) to be visible
        - waiting for" https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kp…" navigation to finish...
        - navigated to "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kp…"


       at ..\src\core\playwright-world.ts:335

      333 |     this.logger.info(`⏳ Waiting for element: ${selector}`);
      334 |     const element = this.parseLocator(selector);
    > 335 |     await element.waitFor({ state: 'visible', timeout });
          |                   ^
      336 |   }
      337 |
      338 |   /**
        at PlaywrightWorld.waitForElement (C:\workspace\Playwright_Automation_Framework\src\core\playwright-world.ts:335:19)
        at Object.handler (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:170:21)
        at StepDefinitionRegistry.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:334:23)
        at PlaywrightCucumberRunner.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:196:31)
        at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:231:20)
        at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9

    attachment #1: Step 1 - Before: Given I navigate to URL "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/" (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-1---Before-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch--0874230e995514f05230eb09df7e564097deb05a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: Step 1 - After: Given I navigate to URL "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/" (3603ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-1---After-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch-3603ms--76e0bc46f32100246452a29546397d4fd4e61208.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: Step 2 - Before: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7b97b6ecd8a021fd44aa7e3cc251f323f5aac40f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: Step 2 - After: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (27ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-27ms--6d4618fa6469d69ce9894daee70fcbd3beb85ca3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: Step 3 - Before: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--a31b3e7c43cb1efea11b3548fc5c4de968169b53.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: Step 3 - After: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (38ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-38ms--9aed25838606bab71a24c398a8193d0e5e7144e6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: Step 4 - Before: And I click on "getByRole('button', { name: 'Next' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--1cb637a407c45e252a89feaa09376d02ecff8032.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: Step 4 - After: And I click on "getByRole('button', { name: 'Next' })" (84ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-84ms--39ca31def62bf7693a9303f1f6530a2005473251.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #9: Step 5 - Before: Then I wait for element "getByRole('link', { name: 'OI Development', exact: true })" to be visible with timeout 60000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-6f394438ac89195e969aa3c0716a8777148d78c7.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: Step 5 - After: Then I wait for element "getByRole('link', { name: 'OI Development', exact: true })" to be visible with timeout 60000 (8731ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-5---After-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-8731ms--89c6bbe0aca926942991f53ad8cefa84e72da041.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: Step 6 - Before: And I wait for page title to contain "OI Development - Home" (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-6---Before-And-I-wait-for-page-title-to-contain-OI-Development---Home--ffa58bd93ab409707aafaddf3ab8cfb725026aab.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #12: Step 6 - After: And I wait for page title to contain "OI Development - Home" (3708ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-6---After-And-I-wait-for-page-title-to-contain-OI-Development---Home-3708ms--f08e39671a2111ca88989554e63b3ac94e863b55.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #13: Step 7 - Before: Then I wait for element "getByRole('combobox', { name: 'Search box. Suggestions' })" to be visible with timeout 60000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-7---Before-Then-I-wait-for-element-getByRole-combobox-name-Search-box-Suggestions-to-be-visible-with-timeout-60000-58373359d7360eb289842f5267d21ae12f42a7f6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #14: Step 7 - After: Then I wait for element "getByRole('combobox', { name: 'Search box. Suggestions' })" to be visible with timeout 60000 (8750ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-7---After-Then-I-wait-for-element-getByRole-combobox-name-Search-box-Suggestions-to-be-visible-with-timeout-60000-8750ms--2c045a4d3cc1bf2a4b686e49aa666c611bdf3a3d.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #15: Step 8 - Before: When I fill "getByRole('combobox', { name: 'Search box. Suggestions' })" with "kpmg" (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-8---Before-When-I-fill-getByRole-combobox-name-Search-box-Suggestions-with-kpmg--f89b4c5e80f021f86ed31cd87734e9ea74982f46.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #16: Step 8 - After: When I fill "getByRole('combobox', { name: 'Search box. Suggestions' })" with "kpmg" (186ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-8---After-When-I-fill-getByRole-combobox-name-Search-box-Suggestions-with-kpmg-186ms--4f7c18327be60247a70633fcb5287d48efafc641.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #17: Step 9 - Before: And I press "Enter" (image/png) ───────────────────────────────
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-9---Before-And-I-press-Enter--ef98a150dc01e4192950ccacfae2e300eef6e242.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #18: Step 9 - After: And I press "Enter" (92ms) (image/png) ─────────────────────────
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-9---After-And-I-press-Enter-92ms--8751f3c02cb1a72fe804962f6dc7fb0accc8e6c2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #19: Step 10 - Before: And I wait for page title to contain "Results" (image/png) ───
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-10---Before-And-I-wait-for-page-title-to-contain-Results--fd1995806bcdcafdff5d0d5abd0a06cc44142d58.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #20: Step 10 - After: And I wait for page title to contain "Results" (2223ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-10---After-And-I-wait-for-page-title-to-contain-Results-2223ms--1d46c4ddc7ef01f814fc388ec39bf0fca64bf19e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #21: Step 11 - Before: Then I wait for element "getByRole('link', { name: 'Organizational Logo' })" to be visible with timeout 60000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-11---Before-Then-I-wait-for-element-getByRole-link-name-Organizational-Logo-to-be-visible-with-timeout-60000-034b8f7842b82a022974e73de932847d9621310b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #22: Step 11 - After: Then I wait for element "getByRole('link', { name: 'Organizational Logo' })" to be visible with timeout 60000 (2375ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-11---After-Then-I-wait-for-element-getByRole-link-name-Organizational-Logo-to-be-visible-with-timeout-60000-2375ms--67b6c4834cba3b6deb36776b7d7831423262fa00.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #23: Step 12 - Before: Then I wait for "getByRole('button', { name: 'All', exact: true })" to be visible with timeout 30000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-12---Before-Then-I-wait-for-getByRole-button-name-All-exact-true-to-be-visible-with-timeout-30000-6898384945fd0dac2626220256b2cdf64eb6b973.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #24: Failure Screenshot (image/png) ─────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Failure-Screenshot-cb449921f9855491b5761c09290439149d1aab4f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #25: screenshot (image/png) ─────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #26: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\error-context.md

    attachment #28: trace (application/zip) ────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\trace.zip
    Usage:

        npx playwright show-trace test-results\playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🎬 Starting scenario: TC0101- Login and Check the Default Data Source Order
📁 Feature: SPO Health Check
🏷️ Tags: @combined, @spo
📋 Step 1/34: Given I navigate to URL "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/"
✅ Step completed in 3603ms
📋 Step 2/34: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
✅ Step completed in 27ms
📋 Step 3/34: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>"
✅ Step completed in 38ms
📋 Step 4/34: And I click on "getByRole('button', { name: 'Next' })"
✅ Step completed in 84ms
📋 Step 5/34: Then I wait for element "getByRole('link', { name: 'OI Development', exact: true })" to be visible with timeout 60000
✅ Step completed in 8731ms
📋 Step 6/34: And I wait for page title to contain "OI Development - Home"
[[33mwarn[39m]: Error getting title, retrying... (page.title: Execution context was destroyed, most likely because of a navigation)
✅ Step completed in 3708ms
📋 Step 7/34: Then I wait for element "getByRole('combobox', { name: 'Search box. Suggestions' })" to be visible with timeout 60000
✅ Step completed in 8750ms
📋 Step 8/34: When I fill "getByRole('combobox', { name: 'Search box. Suggestions' })" with "kpmg"
✅ Step completed in 186ms
📋 Step 9/34: And I press "Enter"
✅ Step completed in 92ms
📋 Step 10/34: And I wait for page title to contain "Results"
✅ Step completed in 2223ms
📋 Step 11/34: Then I wait for element "getByRole('link', { name: 'Organizational Logo' })" to be visible with timeout 60000
✅ Step completed in 2375ms
📋 Step 12/34: Then I wait for "getByRole('button', { name: 'All', exact: true })" to be visible with timeout 30000
[[31merror[39m]: ❌ Step failed: Then I wait for "getByRole('button', { name: 'All', exact: true })" to be visible with timeout 30000

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-1---Before-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch--0874230e995514f05230eb09df7e564097deb05a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-1---After-Given-I-navigate-to-URL-https-spo-global-kpmg-com-sites-GO-oi-bus-kpmgfinddeventerprisesearch-3603ms--76e0bc46f32100246452a29546397d4fd4e61208.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7b97b6ecd8a021fd44aa7e3cc251f323f5aac40f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-27ms--6d4618fa6469d69ce9894daee70fcbd3beb85ca3.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--a31b3e7c43cb1efea11b3548fc5c4de968169b53.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-38ms--9aed25838606bab71a24c398a8193d0e5e7144e6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--1cb637a407c45e252a89feaa09376d02ecff8032.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-84ms--39ca31def62bf7693a9303f1f6530a2005473251.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-6f394438ac89195e969aa3c0716a8777148d78c7.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-5---After-Then-I-wait-for-element-getByRole-link-name-OI-Development-exact-true-to-be-visible-with-timeout-60000-8731ms--89c6bbe0aca926942991f53ad8cefa84e72da041.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-6---Before-And-I-wait-for-page-title-to-contain-OI-Development---Home--ffa58bd93ab409707aafaddf3ab8cfb725026aab.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-6---After-And-I-wait-for-page-title-to-contain-OI-Development---Home-3708ms--f08e39671a2111ca88989554e63b3ac94e863b55.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-7---Before-Then-I-wait-for-element-getByRole-combobox-name-Search-box-Suggestions-to-be-visible-with-timeout-60000-58373359d7360eb289842f5267d21ae12f42a7f6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-7---After-Then-I-wait-for-element-getByRole-combobox-name-Search-box-Suggestions-to-be-visible-with-timeout-60000-8750ms--2c045a4d3cc1bf2a4b686e49aa666c611bdf3a3d.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-8---Before-When-I-fill-getByRole-combobox-name-Search-box-Suggestions-with-kpmg--f89b4c5e80f021f86ed31cd87734e9ea74982f46.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-8---After-When-I-fill-getByRole-combobox-name-Search-box-Suggestions-with-kpmg-186ms--4f7c18327be60247a70633fcb5287d48efafc641.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-9---Before-And-I-press-Enter--ef98a150dc01e4192950ccacfae2e300eef6e242.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-9---After-And-I-press-Enter-92ms--8751f3c02cb1a72fe804962f6dc7fb0accc8e6c2.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-10---Before-And-I-wait-for-page-title-to-contain-Results--fd1995806bcdcafdff5d0d5abd0a06cc44142d58.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-10---After-And-I-wait-for-page-title-to-contain-Results-2223ms--1d46c4ddc7ef01f814fc388ec39bf0fca64bf19e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-11---Before-Then-I-wait-for-element-getByRole-link-name-Organizational-Logo-to-be-visible-with-timeout-60000-034b8f7842b82a022974e73de932847d9621310b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-11---After-Then-I-wait-for-element-getByRole-link-name-Organizational-Logo-to-be-visible-with-timeout-60000-2375ms--67b6c4834cba3b6deb36776b7d7831423262fa00.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Step-12---Before-Then-I-wait-for-getByRole-button-name-All-exact-true-to-be-visible-with-timeout-30000-6898384945fd0dac2626220256b2cdf64eb6b973.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\attachments\Failure-Screenshot-cb449921f9855491b5761c09290439149d1aab4f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\test-failed-1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\video.webm]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\error-context.md]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-SPO-Healt-4e3d9-e-Default-Data-Source-Order-chromium\trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Scenario failed: TC0101- Login and Check the Default Data Source Order locator.waitFor: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for getByRole('button', { name: 'All', exact: true }) to be visible[22m
[2m    - waiting for" https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kp…" navigation to finish...[22m
[2m    - navigated to "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kp…"[22m

    at PlaywrightWorld.waitForElement [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-world.ts:335:19[90m)[39m
    at Object.handler [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:170:21[90m)[39m
    at StepDefinitionRegistry.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:334:23[90m)[39m
    at PlaywrightCucumberRunner.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:196:31[90m)[39m
    at PlaywrightCucumberRunner.executeScenario [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:231:20[90m)[39m
    at [90mC:\workspace\Playwright_Automation_Framework\[39mtests\cucumber-wrapper.spec.ts:45:9 {
  name: [32m'TimeoutError'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@74'[39m,
    location: {
      file: [32m'C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts'[39m,
      line: [33m335[39m,
      column: [33m19[39m,
      function: [32m'PlaywrightWorld.waitForElement'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m"locator.getByRole('button', { name: 'All', exact: true }).waitFor"[39m,
    apiName: [32m'locator.waitFor'[39m,
    params: {
      selector: [32m'internal:role=button[name="All"s]'[39m,
      strict: [33mtrue[39m,
      omitReturnValue: [33mtrue[39m,
      state: [32m'visible'[39m,
      timeout: [33m30000[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@74'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1749108677765[39m,
    error: {
      message: [32m'TimeoutError: locator.waitFor: Timeout 30000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for getByRole('button', { name: 'All', exact: true }) to be visible\x1B[22m\n"[39m +
        [32m'\x1B[2m    - waiting for" https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kp…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kp…"\x1B[22m\n'[39m,
      stack: [32m'TimeoutError: locator.waitFor: Timeout 30000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for getByRole('button', { name: 'All', exact: true }) to be visible\x1B[22m\n"[39m +
        [32m'\x1B[2m    - waiting for" https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kp…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/SitePages/Results.aspx?q=kpmg&datasource=intranet&origin=portal&caller=https%3A%2F%2Fspo-global.kpmg.com%2Fsites%2FGO-oi-bus-kp…"\x1B[22m\n'[39m +
        [32m'\n'[39m +
        [32m'    at PlaywrightWorld.waitForElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:335:19)\n'[39m +
        [32m'    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:170:21)\n'[39m +
        [32m'    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:334:23)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\n'[39m +
        [32m'    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
</testsuite>
</testsuites>