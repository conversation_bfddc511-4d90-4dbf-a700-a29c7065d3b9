{"version": 3, "file": "browser.manager.js", "sourceRoot": "", "sources": ["../../src/core/browser.manager.ts"], "names": [], "mappings": ";;;AAAA,2CAAqG;AACrG,qDAAiD;AACjD,4CAAyC;AACzC,uDAAmD;AACnD,qEAAgE;AAEhE;;;GAGG;AACH,MAAa,cAAc;IACjB,MAAM,CAAC,QAAQ,CAAiB;IAChC,OAAO,GAAmB,IAAI,CAAC;IAC/B,OAAO,GAA0B,IAAI,CAAC;IACtC,IAAI,GAAgB,IAAI,CAAC;IACzB,MAAM,GAAG,IAAI,eAAM,CAAC,gBAAgB,CAAC,CAAC;IACtC,oBAAoB,GAAgC,IAAI,CAAC;IAEjE;;OAEG;IACH,gBAAuB,CAAC;IAExB;;;OAGG;IACI,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC7B,cAAc,CAAC,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,cAAc,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,WAAW;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,sBAAS,CAAC,OAAO,UAAU,CAAC,CAAC;YAE9D,IAAI,CAAC;gBACH,+CAA+C;gBAC/C,MAAM,aAAa,GAAG;oBACpB,QAAQ,EAAE,sBAAS,CAAC,QAAQ;oBAC5B,MAAM,EAAE,sBAAS,CAAC,OAAO;oBACzB,OAAO,EAAE,sBAAS,CAAC,eAAe,IAAI,SAAS;oBAC/C,OAAO,EAAE,KAAK,EAAE,iCAAiC;oBACjD,+CAA+C;oBAC/C,GAAG,CAAC,sBAAS,CAAC,eAAe,IAAI;wBAC/B,IAAI,EAAE,IAAI,CAAC,qBAAqB,EAAE;qBACnC,CAAC;oBACF,oDAAoD;oBACpD,GAAG,CAAC,sBAAS,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,MAAM,IAAI;wBAChD,cAAc,EAAE,SAAS,CAAC,yCAAyC;qBACpE,CAAC;iBACH,CAAC;gBAEF,8CAA8C;gBAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,sBAAS,CAAC,OAAO,0BAA0B,sBAAS,CAAC,eAAe,IAAI,SAAS,EAAE,CAAC,CAAC;gBACnH,IAAI,sBAAS,CAAC,eAAe,EAAE,CAAC;oBAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBACjD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;oBACtF,OAAO,CAAC,GAAG,CAAC,+BAA+B,sBAAS,CAAC,OAAO,GAAG,EAAE,WAAW,CAAC,CAAC;gBAChF,CAAC;gBAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;gBAE7E,QAAQ,sBAAS,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;oBACxC,KAAK,SAAS;wBACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;wBAC9C,IAAI,CAAC,OAAO,GAAG,MAAM,cAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;wBACnD,MAAM;oBACR,KAAK,QAAQ;wBACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;wBAC7C,IAAI,CAAC,OAAO,GAAG,MAAM,aAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;wBAClD,MAAM;oBACR,KAAK,MAAM;wBACT,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;wBAE3C,+CAA+C;wBAC/C,IAAI,sBAAS,CAAC,eAAe,EAAE,CAAC;4BAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;4BAC7E,IAAI,CAAC;gCACH,IAAI,CAAC,oBAAoB,GAAG,IAAI,6CAAoB,EAAE,CAAC;gCACvD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,CAAC;gCAEtE,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;gCAClC,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;gCAClC,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;gCAE5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;gCAChE,OAAO,IAAI,CAAC,OAAO,CAAC;4BAEtB,CAAC;4BAAC,OAAO,cAAc,EAAE,CAAC;gCACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,cAAc,EAAE,CAAC,CAAC;gCACrE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;gCACzD,2CAA2C;4BAC7C,CAAC;wBACH,CAAC;wBAED,sEAAsE;wBACtE,IAAI,CAAC;4BACH,gEAAgE;4BAChE,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;gCAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;gCACrE,IAAI,CAAC,OAAO,GAAG,MAAM,eAAQ,CAAC,MAAM,CAAC;oCACnC,GAAG,aAAa;oCAChB,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;iCACtC,CAAC,CAAC;4BACL,CAAC;iCAAM,CAAC;gCACN,4DAA4D;gCAC5D,IAAI,CAAC,OAAO,GAAG,MAAM,eAAQ,CAAC,MAAM,CAAC;oCACnC,GAAG,aAAa;oCAChB,OAAO,EAAE,QAAQ;oCACjB,iBAAiB,EAAE,CAAC,sBAAsB,CAAC;iCAC5C,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,SAAS,EAAE,CAAC;4BACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,SAAS,EAAE,CAAC,CAAC;4BACzD,oCAAoC;4BACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;4BACzD,IAAI,CAAC,OAAO,GAAG,MAAM,eAAQ,CAAC,MAAM,CAAC;gCACnC,QAAQ,EAAE,sBAAS,CAAC,QAAQ;gCAC5B,OAAO,EAAE,QAAQ;gCACjB,iBAAiB,EAAE,IAAI;gCACvB,IAAI,EAAE;oCACJ,cAAc;oCACd,0BAA0B;oCAC1B,yBAAyB;oCACzB,iCAAiC;oCACjC,gBAAgB;oCAChB,aAAa;oCACb,eAAe;oCACf,cAAc;oCACd,iCAAiC;oCACjC,uCAAuC;oCACvC,0CAA0C;oCAC1C,oBAAoB;oCACpB,sDAAsD;oCACtD,sBAAsB;oCACtB,gCAAgC;oCAChC,mCAAmC;oCACnC,kCAAkC;oCAClC,qBAAqB;oCACrB,wBAAwB;oCACxB,qBAAqB;iCACtB;6BACF,CAAC,CAAC;wBACL,CAAC;wBACD,MAAM;oBACR,KAAK,QAAQ;wBACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;wBAC7C,kCAAkC;wBAClC,IAAI,CAAC,OAAO,GAAG,MAAM,eAAQ,CAAC,MAAM,CAAC;4BACnC,GAAG,aAAa;4BAChB,OAAO,EAAE,QAAQ;yBAClB,CAAC,CAAC;wBACH,MAAM;oBACR,KAAK,UAAU,CAAC;oBAChB;wBACE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;wBAC/C,IAAI,CAAC,OAAO,GAAG,MAAM,eAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;wBACpD,MAAM;gBACV,CAAC;gBAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YACvD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;gBAE5D,sDAAsD;gBACtD,IAAI,sBAAS,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,UAAU,EAAE,CAAC;oBACnD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;oBACrD,IAAI,CAAC;wBACH,IAAI,CAAC,OAAO,GAAG,MAAM,eAAQ,CAAC,MAAM,CAAC;4BACnC,QAAQ,EAAE,sBAAS,CAAC,QAAQ;4BAC5B,MAAM,EAAE,sBAAS,CAAC,OAAO;4BACzB,OAAO,EAAE,KAAK;yBACf,CAAC,CAAC;wBACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;oBACtD,CAAC;oBAAC,OAAO,aAAa,EAAE,CAAC;wBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,aAAa,EAAE,CAAC,CAAC;wBACxE,MAAM,aAAa,CAAC;oBACtB,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,MAAM,QAAQ,GAAG;YACf,gBAAgB;YAChB,4BAA4B;YAC5B,uCAAuC;YACvC,0CAA0C;YAC1C,kCAAkC;YAClC,wBAAwB;YACxB,yCAAyC;SAC1C,CAAC;QAEF,QAAQ,sBAAS,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;YACxC,KAAK,MAAM;gBACT,OAAO;oBACL,aAAa,EAAe,8CAA8C;oBAC1E,cAAc,EAAc,mBAAmB;oBAC/C,sBAAsB,EAAM,qCAAqC;oBACjE,wBAAwB,EAAI,8BAA8B;oBAC1D,gBAAgB;oBAChB,4BAA4B;oBAC5B,uCAAuC;oBACvC,0CAA0C;oBAC1C,kCAAkC;oBAClC,yCAAyC;oBACzC,+CAA+C;oBAC/C,yBAAyB;oBACzB,sBAAsB,CAAM,0BAA0B;iBACvD,CAAC;YAEJ,KAAK,QAAQ,CAAC;YACd,KAAK,UAAU;gBACb,OAAO;oBACL,aAAa,EAAe,wBAAwB;oBACpD,sBAAsB,EAAM,kCAAkC;oBAC9D,GAAG,QAAQ;iBACZ,CAAC;YAEJ,KAAK,SAAS;gBACZ,OAAO;oBACL,kBAAkB,EAAU,2BAA2B;oBACvD,gBAAgB,EAAY,uBAAuB;oBACnD,GAAG,QAAQ;iBACZ,CAAC;YAEJ,KAAK,QAAQ;gBACX,OAAO;oBACL,oBAAoB,EAAQ,0BAA0B;oBACtD,GAAG,QAAQ;iBACZ,CAAC;YAEJ;gBACE,qCAAqC;gBACrC,OAAO;oBACL,aAAa;oBACb,GAAG,QAAQ;iBACZ,CAAC;QACN,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,WAAW;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC;QAED,mEAAmE;QACnE,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAEjD,kBAAkB;YAClB,MAAM,cAAc,GAAQ,EAAE,CAAC;YAE/B,mBAAmB;YACnB,IAAI,sBAAS,CAAC,WAAW,IAAI,cAAO,CAAC,sBAAS,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC5D,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,cAAO,CAAC,sBAAS,CAAC,WAAW,CAAC,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,kBAAkB;gBAClB,cAAc,CAAC,QAAQ,GAAG;oBACxB,KAAK,EAAE,sBAAS,CAAC,cAAc;oBAC/B,MAAM,EAAE,sBAAS,CAAC,eAAe;iBAClC,CAAC;YACJ,CAAC;YAED,kBAAkB;YAClB,IAAI,sBAAS,CAAC,eAAe,EAAE,CAAC;gBAC9B,cAAc,CAAC,WAAW,GAAG;oBAC3B,GAAG,EAAE,sBAAS,CAAC,SAAS,IAAI,wBAAwB;oBACpD,IAAI,EAAE;wBACJ,KAAK,EAAE,sBAAS,CAAC,WAAW,IAAI,IAAI;wBACpC,MAAM,EAAE,sBAAS,CAAC,YAAY,IAAI,GAAG;qBACtC;iBACF,CAAC;YACJ,CAAC;YAED,gCAAgC;YAChC,IAAI,sBAAS,CAAC,eAAe,EAAE,CAAC;gBAC9B,uDAAuD;gBACvD,cAAc,CAAC,YAAY,GAAG,SAAS,CAAC;gBACxC,wCAAwC;gBACxC,cAAc,CAAC,eAAe,GAAG,KAAK,CAAC;gBACvC,gCAAgC;gBAChC,cAAc,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBACxC,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC;gBAEhC,2DAA2D;gBAC3D,IAAI,sBAAS,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE,CAAC;oBAC/C,gCAAgC;oBAChC,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC;oBACnC,cAAc,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBACxC,oCAAoC;oBACpC,cAAc,CAAC,cAAc,GAAG,OAAO,CAAC;oBACxC,oBAAoB;oBACpB,cAAc,CAAC,YAAY,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;oBAC3D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;gBAC1F,CAAC;qBAAM,CAAC;oBACN,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC;oBACnC,cAAc,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;gBAC3E,CAAC;YACH,CAAC;YAED,cAAc;YACd,IAAI,sBAAS,CAAC,YAAY,IAAI,sBAAS,CAAC,aAAa,EAAE,CAAC;gBACtD,cAAc,CAAC,WAAW,GAAG;oBAC3B,QAAQ,EAAE,UAAU,CAAC,sBAAS,CAAC,YAAY,CAAC;oBAC5C,SAAS,EAAE,UAAU,CAAC,sBAAS,CAAC,aAAa,CAAC;iBAC/C,CAAC;YACJ,CAAC;YAED,cAAc;YACd,IAAI,sBAAS,CAAC,WAAW,EAAE,CAAC;gBAC1B,cAAc,CAAC,WAAW,GAAG,sBAAS,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChE,CAAC;YAED,sBAAsB;YACtB,IAAI,sBAAS,CAAC,MAAM,EAAE,CAAC;gBACrB,cAAc,CAAC,MAAM,GAAG,sBAAS,CAAC,MAAM,CAAC;YAC3C,CAAC;YAED,IAAI,sBAAS,CAAC,WAAW,EAAE,CAAC;gBAC1B,cAAc,CAAC,UAAU,GAAG,sBAAS,CAAC,WAAW,CAAC;YACpD,CAAC;YAED,qBAAqB;YACrB,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,OAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;YAE9D,+BAA+B;YAC/B,IAAI,sBAAS,CAAC,aAAa,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;oBAC/B,WAAW,EAAE,sBAAS,CAAC,iBAAiB;oBACxC,SAAS,EAAE,sBAAS,CAAC,eAAe;oBACpC,OAAO,EAAE,sBAAS,CAAC,aAAa;iBACjC,CAAC,CAAC;YACL,CAAC;YAED,qCAAqC;YACrC,IAAI,sBAAS,CAAC,yBAAyB,IAAI,sBAAS,CAAC,yBAAyB,EAAE,CAAC;gBAC/E,MAAM,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;oBACpC,QAAQ,EAAE,sBAAS,CAAC,yBAAyB;oBAC7C,QAAQ,EAAE,sBAAS,CAAC,yBAAyB;iBAC9C,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,QAAQ;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC;QAED,gEAAgE;QAChE,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEtC,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,OAAQ,CAAC,OAAO,EAAE,CAAC;YAE1C,iCAAiC;YACjC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,sBAAS,CAAC,kBAAkB,CAAC,CAAC;YACpE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,sBAAS,CAAC,eAAe,CAAC,CAAC;YAEvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;;OAGG;IACI,OAAO;QACZ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACtB,OAAO,gCAAc,CAAC,WAAW,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY;QACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAEtD,IAAI,IAAI,CAAC,OAAO,IAAI,sBAAS,CAAC,aAAa,EAAE,CAAC;YAC5C,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC9B,IAAI,EAAE,0BAA0B;aACjC,CAAC,CAAC;QACL,CAAC;QAED,+CAA+C;QAC/C,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;YAC1C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IAClD,CAAC;CACF;AAxbD,wCAwbC"}