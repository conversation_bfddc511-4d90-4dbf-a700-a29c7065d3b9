"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.EdgeInPrivateLauncher = void 0;
const child_process_1 = require("child_process");
const playwright_1 = require("playwright");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * Edge InPrivate Launcher
 *
 * This class provides a workaround for launching Edge in true InPrivate mode
 * when Playwright's standard approach doesn't work properly.
 */
class EdgeInPrivateLauncher {
    edgeProcess = null;
    browser = null;
    /**
     * Find Edge executable path on Windows
     */
    findEdgeExecutable() {
        const edgePaths = [
            'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe',
            'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe',
            process.env.PROGRAMFILES + '\\Microsoft\\Edge\\Application\\msedge.exe',
            process.env['PROGRAMFILES(X86)'] + '\\Microsoft\\Edge\\Application\\msedge.exe'
        ];
        for (const edgePath of edgePaths) {
            if (fs.existsSync(edgePath)) {
                return edgePath;
            }
        }
        return null;
    }
    /**
     * Launch Edge in InPrivate mode directly
     */
    async launchEdgeInPrivateDirect(url) {
        const edgeExePath = this.findEdgeExecutable();
        if (!edgeExePath) {
            console.error('Edge executable not found');
            return false;
        }
        try {
            const args = [
                '--inprivate',
                '--new-window',
                '--disable-extensions',
                '--no-first-run',
                '--no-default-browser-check'
            ];
            if (url) {
                args.push(url);
            }
            this.edgeProcess = (0, child_process_1.spawn)(edgeExePath, args, {
                detached: false,
                stdio: 'pipe'
            });
            console.log('✅ Edge launched directly in InPrivate mode');
            return true;
        }
        catch (error) {
            console.error('❌ Failed to launch Edge directly:', error);
            return false;
        }
    }
    /**
     * Launch Edge with Playwright but force InPrivate-like behavior
     */
    async launchEdgeWithPlaywright(headless = false) {
        try {
            // First, try to close any existing Edge instances
            await this.closeExistingEdgeInstances();
            // Launch Edge with Playwright using enhanced arguments
            this.browser = await playwright_1.chromium.launch({
                channel: 'msedge',
                headless,
                args: [
                    '--inprivate',
                    '--new-window',
                    '--disable-extensions',
                    '--disable-web-security',
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--force-new-instance',
                    '--user-data-dir=' + path.join(process.cwd(), 'temp-edge-profile-' + Date.now())
                ]
            });
            console.log('✅ Edge launched with Playwright (InPrivate mode)');
            return this.browser;
        }
        catch (error) {
            console.error('❌ Failed to launch Edge with Playwright:', error);
            return null;
        }
    }
    /**
     * Create InPrivate-like context
     */
    async createInPrivateContext(browser) {
        const context = await browser.newContext({
            // Force clean state
            storageState: { cookies: [], origins: [] },
            acceptDownloads: false,
            ignoreHTTPSErrors: true,
            bypassCSP: true,
            serviceWorkers: 'block'
        });
        // Clear any existing storage on each page
        context.on('page', async (page) => {
            await page.addInitScript(() => {
                // Clear all storage
                if (window.localStorage) {
                    window.localStorage.clear();
                }
                if (window.sessionStorage) {
                    window.sessionStorage.clear();
                }
                // Clear cookies
                document.cookie.split(";").forEach(function (c) {
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                });
            });
        });
        return context;
    }
    /**
     * Close existing Edge instances
     */
    async closeExistingEdgeInstances() {
        try {
            // On Windows, try to close Edge processes
            if (process.platform === 'win32') {
                (0, child_process_1.spawn)('taskkill', ['/F', '/IM', 'msedge.exe'], { stdio: 'ignore' });
                // Wait a bit for processes to close
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        catch (error) {
            // Ignore errors - Edge might not be running
        }
    }
    /**
     * Cleanup
     */
    async cleanup() {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
        }
        if (this.edgeProcess) {
            this.edgeProcess.kill();
            this.edgeProcess = null;
        }
    }
}
exports.EdgeInPrivateLauncher = EdgeInPrivateLauncher;
//# sourceMappingURL=edge-inprivate-launcher.js.map