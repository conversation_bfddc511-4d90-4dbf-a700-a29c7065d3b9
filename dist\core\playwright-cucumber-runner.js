"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlaywrightCucumberRunner = void 0;
const logger_1 = require("../utils/logger");
const step_definition_registry_1 = require("./step-definition-registry");
const playwright_world_1 = require("./playwright-world");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const glob_1 = require("glob");
class PlaywrightCucumberRunner {
    logger;
    stepRegistry;
    features = [];
    constructor() {
        this.logger = new logger_1.Logger('PlaywrightCucumberRunner');
        this.stepRegistry = new step_definition_registry_1.StepDefinitionRegistry();
    }
    /**
     * Load and parse Cucumber feature files synchronously
     */
    loadFeaturesSync(pattern = 'src/features/**/*.feature') {
        const featureFiles = require('glob').sync(pattern);
        const features = [];
        for (const file of featureFiles) {
            try {
                const content = fs.readFileSync(file, 'utf8');
                const feature = this.parseFeature(content, file);
                if (feature) {
                    features.push(feature);
                }
            }
            catch (error) {
                this.logger.error(`Failed to parse feature file ${file}:`, error);
            }
        }
        this.logger.info(`Loaded ${features.length} feature files`);
        this.features = features;
        return features;
    }
    /**
     * Load and parse Cucumber feature files
     */
    async loadFeatures(pattern = 'src/features/**/*.feature') {
        const featureFiles = await (0, glob_1.glob)(pattern);
        for (const file of featureFiles) {
            try {
                const content = fs.readFileSync(file, 'utf8');
                const feature = this.parseFeature(content, file);
                if (feature) {
                    this.features.push(feature);
                }
            }
            catch (error) {
                this.logger.error(`Failed to parse feature file ${file}:`, error);
            }
        }
        this.logger.info(`Loaded ${this.features.length} feature files`);
    }
    /**
     * Parse a Cucumber feature file
     */
    parseFeature(content, file) {
        const lines = content.split('\n');
        let currentLine = 0;
        // Skip to feature line
        while (currentLine < lines.length && !lines[currentLine].trim().startsWith('Feature:')) {
            currentLine++;
        }
        if (currentLine >= lines.length) {
            return null;
        }
        const featureLine = lines[currentLine].trim();
        const featureName = featureLine.replace('Feature:', '').trim();
        // Get feature tags (lines before feature)
        const featureTags = [];
        let tagLine = currentLine - 1;
        while (tagLine >= 0 && lines[tagLine].trim().startsWith('@')) {
            featureTags.push(...lines[tagLine].trim().split(/\s+/));
            tagLine--;
        }
        // Get feature description
        currentLine++;
        let description = '';
        while (currentLine < lines.length && !lines[currentLine].trim().startsWith('Scenario') && !lines[currentLine].trim().startsWith('@')) {
            if (lines[currentLine].trim()) {
                description += lines[currentLine].trim() + ' ';
            }
            currentLine++;
        }
        // Parse scenarios
        const scenarios = [];
        while (currentLine < lines.length) {
            // Look for scenario tags
            const scenarioTags = [...featureTags];
            while (currentLine < lines.length && lines[currentLine].trim().startsWith('@')) {
                scenarioTags.push(...lines[currentLine].trim().split(/\s+/));
                currentLine++;
            }
            // Look for scenario line
            if (currentLine < lines.length && lines[currentLine].trim().startsWith('Scenario:')) {
                const scenarioName = lines[currentLine].trim().replace('Scenario:', '').trim();
                currentLine++;
                // Parse steps
                const steps = [];
                while (currentLine < lines.length &&
                    !lines[currentLine].trim().startsWith('Scenario') &&
                    !lines[currentLine].trim().startsWith('@') &&
                    !lines[currentLine].trim().startsWith('Feature:')) {
                    const line = lines[currentLine].trim();
                    if (line && (line.startsWith('Given') || line.startsWith('When') || line.startsWith('Then') || line.startsWith('And') || line.startsWith('But'))) {
                        const parts = line.split(/\s+/);
                        const keyword = parts[0];
                        const text = parts.slice(1).join(' ');
                        steps.push({
                            keyword,
                            text,
                            line: currentLine + 1
                        });
                    }
                    currentLine++;
                }
                if (steps.length > 0) {
                    scenarios.push({
                        name: scenarioName,
                        tags: scenarioTags,
                        steps,
                        feature: featureName
                    });
                }
            }
            else {
                currentLine++;
            }
        }
        return {
            name: featureName,
            description: description.trim(),
            tags: featureTags,
            scenarios,
            file
        };
    }
    /**
     * Execute a Cucumber step using the step definitions
     */
    async executeStep(world, stepText, testInfo) {
        try {
            await this.stepRegistry.executeStep(world, stepText);
        }
        catch (error) {
            this.logger.error(`❌ Step failed: ${stepText}`, error);
            throw error;
        }
    }
    /**
     * Execute a single scenario
     */
    async executeScenario(scenario, page, context, testInfo) {
        // Initialize Playwright world
        const world = new playwright_world_1.PlaywrightWorld();
        await world.init(page, context);
        try {
            // Execute each step
            for (let i = 0; i < scenario.steps.length; i++) {
                const step = scenario.steps[i];
                const stepText = `${step.keyword} ${step.text}`;
                console.log(`📋 Step ${i + 1}/${scenario.steps.length}: ${stepText}`);
                // Take screenshot before step
                const beforeScreenshot = path.join(testInfo.outputDir, `step-${i + 1}-before.png`);
                await page.screenshot({ path: beforeScreenshot, fullPage: true });
                await testInfo.attach(`Step ${i + 1} - Before: ${stepText}`, {
                    path: beforeScreenshot,
                    contentType: 'image/png'
                });
                // Execute the step
                const startTime = Date.now();
                await this.executeStep(world, stepText, testInfo);
                const duration = Date.now() - startTime;
                // Take screenshot after step
                const afterScreenshot = path.join(testInfo.outputDir, `step-${i + 1}-after.png`);
                await page.screenshot({ path: afterScreenshot, fullPage: true });
                await testInfo.attach(`Step ${i + 1} - After: ${stepText} (${duration}ms)`, {
                    path: afterScreenshot,
                    contentType: 'image/png'
                });
                console.log(`✅ Step completed in ${duration}ms`);
            }
            console.log(`🎉 Scenario completed successfully: ${scenario.name}`);
            // Attach video if available
            if (testInfo.attachments) {
                const videoAttachment = testInfo.attachments.find((a) => a.name === 'video');
                if (videoAttachment) {
                    console.log(`🎬 Video recorded: ${videoAttachment.path}`);
                }
            }
            // Attach trace if available
            const tracePath = path.join(testInfo.outputDir, 'trace.zip');
            if (await this.fileExists(tracePath)) {
                await testInfo.attach('Trace', {
                    path: tracePath,
                    contentType: 'application/zip'
                });
                console.log(`🔍 Trace recorded: ${tracePath}`);
            }
        }
        catch (error) {
            console.error(`❌ Scenario failed: ${scenario.name}`, error);
            // Take failure screenshot
            const failureScreenshot = path.join(testInfo.outputDir, 'failure-screenshot.png');
            await page.screenshot({ path: failureScreenshot, fullPage: true });
            await testInfo.attach('Failure Screenshot', {
                path: failureScreenshot,
                contentType: 'image/png'
            });
            throw error;
        }
        finally {
            await world.cleanup();
        }
    }
    /**
     * Check if scenario tags match the filter
     */
    matchesTags(tags, filter) {
        if (!filter)
            return true;
        // Simple tag matching - supports @tag format
        const filterTags = filter.split(/\s+/).map(tag => tag.trim());
        return filterTags.some(filterTag => tags.some(tag => tag === filterTag));
    }
    /**
     * Check if file exists
     */
    async fileExists(filePath) {
        try {
            await fs.promises.access(filePath);
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.PlaywrightCucumberRunner = PlaywrightCucumberRunner;
//# sourceMappingURL=playwright-cucumber-runner.js.map