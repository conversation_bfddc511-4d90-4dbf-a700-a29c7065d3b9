"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BrowserManager = void 0;
const test_1 = require("@playwright/test");
const env_config_1 = require("../config/env.config");
const logger_1 = require("../utils/logger");
const locator_manager_1 = require("./locator.manager");
const edge_inprivate_manager_1 = require("./edge-inprivate-manager");
/**
 * Browser Manager class
 * Manages browser instances, contexts, and pages
 */
class BrowserManager {
    static instance;
    browser = null;
    context = null;
    page = null;
    logger = new logger_1.Logger('BrowserManager');
    edgeInPrivateManager = null;
    /**
     * Private constructor to enforce singleton pattern
     */
    constructor() { }
    /**
     * Get the singleton instance of BrowserManager
     * @returns The BrowserManager instance
     */
    static getInstance() {
        if (!BrowserManager.instance) {
            BrowserManager.instance = new BrowserManager();
        }
        return BrowserManager.instance;
    }
    /**
     * Initialize the browser
     * @returns The initialized browser instance
     */
    async initBrowser() {
        if (!this.browser) {
            this.logger.info(`Initializing ${env_config_1.EnvConfig.BROWSER} browser`);
            try {
                // Simplified launch options to avoid conflicts
                const launchOptions = {
                    headless: env_config_1.EnvConfig.HEADLESS,
                    slowMo: env_config_1.EnvConfig.SLOW_MO,
                    channel: env_config_1.EnvConfig.BROWSER_CHANNEL || undefined,
                    timeout: 60000, // Increase timeout to 60 seconds
                    // Private session arguments (browser-specific)
                    ...(env_config_1.EnvConfig.PRIVATE_SESSION && {
                        args: this.getPrivateSessionArgs()
                    }),
                    // For Edge, ensure we're using the right executable
                    ...(env_config_1.EnvConfig.BROWSER.toLowerCase() === 'edge' && {
                        executablePath: undefined // Let Playwright find Edge automatically
                    })
                };
                // Log the browser configuration for debugging
                this.logger.info(`Launching ${env_config_1.EnvConfig.BROWSER} browser with channel: ${env_config_1.EnvConfig.BROWSER_CHANNEL || 'default'}`);
                if (env_config_1.EnvConfig.PRIVATE_SESSION) {
                    const privateArgs = this.getPrivateSessionArgs();
                    this.logger.info(`Private session enabled with args: ${JSON.stringify(privateArgs)}`);
                    console.log(`🔒 Private session args for ${env_config_1.EnvConfig.BROWSER}:`, privateArgs);
                }
                this.logger.info(`Browser launch options: ${JSON.stringify(launchOptions)}`);
                switch (env_config_1.EnvConfig.BROWSER.toLowerCase()) {
                    case 'firefox':
                        this.logger.info('Launching Firefox browser');
                        this.browser = await test_1.firefox.launch(launchOptions);
                        break;
                    case 'webkit':
                        this.logger.info('Launching WebKit browser');
                        this.browser = await test_1.webkit.launch(launchOptions);
                        break;
                    case 'edge':
                        this.logger.info('Launching Edge browser');
                        // Check if private session is enabled for Edge
                        if (env_config_1.EnvConfig.PRIVATE_SESSION) {
                            this.logger.info('🔒 Edge InPrivate mode requested - using custom launcher');
                            try {
                                this.edgeInPrivateManager = new edge_inprivate_manager_1.EdgeInPrivateManager();
                                const connection = await this.edgeInPrivateManager.launchAndConnect();
                                this.browser = connection.browser;
                                this.context = connection.context;
                                this.page = connection.page;
                                this.logger.info('✅ Edge InPrivate mode launched successfully');
                                return this.browser;
                            }
                            catch (inPrivateError) {
                                this.logger.error(`Edge InPrivate launch failed: ${inPrivateError}`);
                                this.logger.info('Falling back to standard Edge launch');
                                // Continue with standard Edge launch below
                            }
                        }
                        // Standard Edge launch (fallback or when private session is disabled)
                        try {
                            // Try launching Edge with explicit executable path if available
                            if (process.env.EDGE_PATH) {
                                this.logger.info(`Using custom Edge path: ${process.env.EDGE_PATH}`);
                                this.browser = await test_1.chromium.launch({
                                    ...launchOptions,
                                    executablePath: process.env.EDGE_PATH
                                });
                            }
                            else {
                                // Edge is a channel of Chromium with specific configuration
                                this.browser = await test_1.chromium.launch({
                                    ...launchOptions,
                                    channel: 'msedge',
                                    ignoreDefaultArgs: ['--disable-extensions']
                                });
                            }
                        }
                        catch (edgeError) {
                            this.logger.error(`Failed to launch Edge: ${edgeError}`);
                            // Try alternative approach for Edge
                            this.logger.info('Trying alternative approach for Edge');
                            this.browser = await test_1.chromium.launch({
                                headless: env_config_1.EnvConfig.HEADLESS,
                                channel: 'msedge',
                                ignoreDefaultArgs: true,
                                args: [
                                    '--no-sandbox',
                                    '--disable-setuid-sandbox',
                                    '--disable-dev-shm-usage',
                                    '--disable-accelerated-2d-canvas',
                                    '--no-first-run',
                                    '--no-zygote',
                                    '--disable-gpu',
                                    '--mute-audio',
                                    '--disable-background-networking',
                                    '--disable-background-timer-throttling',
                                    '--disable-backgrounding-occluded-windows',
                                    '--disable-breakpad',
                                    '--disable-component-extensions-with-background-pages',
                                    '--disable-extensions',
                                    '--disable-features=TranslateUI',
                                    '--disable-ipc-flooding-protection',
                                    '--disable-renderer-backgrounding',
                                    '--enable-automation',
                                    '--password-store=basic',
                                    '--use-mock-keychain',
                                ]
                            });
                        }
                        break;
                    case 'chrome':
                        this.logger.info('Launching Chrome browser');
                        // Chrome is a channel of Chromium
                        this.browser = await test_1.chromium.launch({
                            ...launchOptions,
                            channel: 'chrome'
                        });
                        break;
                    case 'chromium':
                    default:
                        this.logger.info('Launching Chromium browser');
                        this.browser = await test_1.chromium.launch(launchOptions);
                        break;
                }
                this.logger.info('Browser initialized successfully');
            }
            catch (error) {
                this.logger.error(`Failed to initialize browser: ${error}`);
                // Fallback to Chromium if the specified browser fails
                if (env_config_1.EnvConfig.BROWSER.toLowerCase() !== 'chromium') {
                    this.logger.info('Falling back to Chromium browser');
                    try {
                        this.browser = await test_1.chromium.launch({
                            headless: env_config_1.EnvConfig.HEADLESS,
                            slowMo: env_config_1.EnvConfig.SLOW_MO,
                            timeout: 60000
                        });
                        this.logger.info('Fallback to Chromium successful');
                    }
                    catch (fallbackError) {
                        this.logger.error(`Fallback to Chromium also failed: ${fallbackError}`);
                        throw fallbackError;
                    }
                }
                else {
                    throw error;
                }
            }
        }
        return this.browser;
    }
    /**
     * Get browser-specific private session arguments
     */
    getPrivateSessionArgs() {
        const baseArgs = [
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
        ];
        switch (env_config_1.EnvConfig.BROWSER.toLowerCase()) {
            case 'edge':
                return [
                    '--inprivate', // Edge InPrivate mode - correct flag for Edge
                    '--new-window', // Force new window
                    '--disable-extensions', // Disable extensions in private mode
                    '--disable-web-security', // Allow cross-origin requests
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--force-new-instance' // Force new Edge instance
                ];
            case 'chrome':
            case 'chromium':
                return [
                    '--incognito', // Chrome incognito mode
                    '--disable-extensions', // Disable extensions in incognito
                    ...baseArgs
                ];
            case 'firefox':
                return [
                    '--private-window', // Firefox private browsing
                    '--new-instance', // New Firefox instance
                    ...baseArgs
                ];
            case 'webkit':
                return [
                    '--private-browsing', // WebKit private browsing
                    ...baseArgs
                ];
            default:
                // Fallback to Chrome-style arguments
                return [
                    '--incognito',
                    ...baseArgs
                ];
        }
    }
    /**
     * Initialize the browser context
     * @returns The initialized browser context
     */
    async initContext() {
        if (!this.browser) {
            await this.initBrowser();
        }
        // If Edge InPrivate manager already created the context, return it
        if (this.context && this.edgeInPrivateManager) {
            this.logger.info('Using existing Edge InPrivate context');
            return this.context;
        }
        if (!this.context) {
            this.logger.info('Initializing browser context');
            // Context options
            const contextOptions = {};
            // Device emulation
            if (env_config_1.EnvConfig.DEVICE_NAME && test_1.devices[env_config_1.EnvConfig.DEVICE_NAME]) {
                Object.assign(contextOptions, test_1.devices[env_config_1.EnvConfig.DEVICE_NAME]);
            }
            else {
                // Custom viewport
                contextOptions.viewport = {
                    width: env_config_1.EnvConfig.VIEWPORT_WIDTH,
                    height: env_config_1.EnvConfig.VIEWPORT_HEIGHT
                };
            }
            // Video recording
            if (env_config_1.EnvConfig.VIDEO_RECORDING) {
                contextOptions.recordVideo = {
                    dir: env_config_1.EnvConfig.VIDEO_DIR || './test-results/videos/',
                    size: {
                        width: env_config_1.EnvConfig.VIDEO_WIDTH || 1280,
                        height: env_config_1.EnvConfig.VIDEO_HEIGHT || 720
                    }
                };
            }
            // Private session configuration
            if (env_config_1.EnvConfig.PRIVATE_SESSION) {
                // Ensure no storage state is used for private sessions
                contextOptions.storageState = undefined;
                // Disable persistent storage completely
                contextOptions.acceptDownloads = false;
                // Force incognito-like behavior
                contextOptions.ignoreHTTPSErrors = true;
                contextOptions.bypassCSP = true;
                // For Edge, force InPrivate-like behavior programmatically
                if (env_config_1.EnvConfig.BROWSER.toLowerCase() === 'edge') {
                    // Clear all storage and cookies
                    contextOptions.clearCookies = true;
                    contextOptions.clearLocalStorage = true;
                    // Disable service workers and cache
                    contextOptions.serviceWorkers = 'block';
                    // Force new session
                    contextOptions.storageState = { cookies: [], origins: [] };
                    this.logger.info('Edge private session mode enabled - forcing InPrivate-like behavior');
                }
                else {
                    contextOptions.clearCookies = true;
                    contextOptions.clearLocalStorage = true;
                    this.logger.info('Private session mode enabled - no persistent storage');
                }
            }
            // Geolocation
            if (env_config_1.EnvConfig.GEO_LATITUDE && env_config_1.EnvConfig.GEO_LONGITUDE) {
                contextOptions.geolocation = {
                    latitude: parseFloat(env_config_1.EnvConfig.GEO_LATITUDE),
                    longitude: parseFloat(env_config_1.EnvConfig.GEO_LONGITUDE)
                };
            }
            // Permissions
            if (env_config_1.EnvConfig.PERMISSIONS) {
                contextOptions.permissions = env_config_1.EnvConfig.PERMISSIONS.split(',');
            }
            // Locale and timezone
            if (env_config_1.EnvConfig.LOCALE) {
                contextOptions.locale = env_config_1.EnvConfig.LOCALE;
            }
            if (env_config_1.EnvConfig.TIMEZONE_ID) {
                contextOptions.timezoneId = env_config_1.EnvConfig.TIMEZONE_ID;
            }
            // Create the context
            this.context = await this.browser.newContext(contextOptions);
            // Enable tracing if configured
            if (env_config_1.EnvConfig.TRACE_ENABLED) {
                await this.context.tracing.start({
                    screenshots: env_config_1.EnvConfig.TRACE_SCREENSHOTS,
                    snapshots: env_config_1.EnvConfig.TRACE_SNAPSHOTS,
                    sources: env_config_1.EnvConfig.TRACE_SOURCES
                });
            }
            // Set HTTP credentials if configured
            if (env_config_1.EnvConfig.HTTP_CREDENTIALS_USERNAME && env_config_1.EnvConfig.HTTP_CREDENTIALS_PASSWORD) {
                await this.context.setHTTPCredentials({
                    username: env_config_1.EnvConfig.HTTP_CREDENTIALS_USERNAME,
                    password: env_config_1.EnvConfig.HTTP_CREDENTIALS_PASSWORD
                });
            }
            this.logger.info('Browser context initialized successfully');
        }
        return this.context;
    }
    /**
     * Initialize the page
     * @returns The initialized page
     */
    async initPage() {
        if (!this.context) {
            await this.initContext();
        }
        // If Edge InPrivate manager already created the page, return it
        if (this.page && this.edgeInPrivateManager) {
            this.logger.info('Using existing Edge InPrivate page');
            return this.page;
        }
        if (!this.page) {
            this.logger.info('Initializing page');
            this.page = await this.context.newPage();
            // Set default navigation timeout
            this.page.setDefaultNavigationTimeout(env_config_1.EnvConfig.NAVIGATION_TIMEOUT);
            this.page.setDefaultTimeout(env_config_1.EnvConfig.DEFAULT_TIMEOUT);
            this.logger.info('Page initialized successfully');
        }
        return this.page;
    }
    /**
     * Get the current page
     * @returns The current page
     */
    getPage() {
        if (!this.page) {
            throw new Error('Page not initialized. Call initPage() first.');
        }
        return this.page;
    }
    /**
     * Get the LocatorManager instance
     * @returns The LocatorManager instance
     */
    getLocatorManager() {
        return locator_manager_1.LocatorManager.getInstance();
    }
    /**
     * Close the browser and all associated resources
     */
    async closeBrowser() {
        this.logger.info('Closing browser and all resources');
        if (this.context && env_config_1.EnvConfig.TRACE_ENABLED) {
            await this.context.tracing.stop({
                path: './test-results/trace.zip'
            });
        }
        // Clean up Edge InPrivate manager if it exists
        if (this.edgeInPrivateManager) {
            await this.edgeInPrivateManager.cleanup();
            this.edgeInPrivateManager = null;
        }
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
            this.context = null;
            this.page = null;
        }
        this.logger.info('Browser closed successfully');
    }
}
exports.BrowserManager = BrowserManager;
//# sourceMappingURL=browser.manager.js.map