{"version": 3, "file": "edge-inprivate-launcher.js", "sourceRoot": "", "sources": ["../../src/core/edge-inprivate-launcher.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iDAAoD;AACpD,2CAA+D;AAC/D,uCAAyB;AACzB,2CAA6B;AAE7B;;;;;GAKG;AACH,MAAa,qBAAqB;IACxB,WAAW,GAAwB,IAAI,CAAC;IACxC,OAAO,GAAmB,IAAI,CAAC;IAEvC;;OAEG;IACK,kBAAkB;QACxB,MAAM,SAAS,GAAG;YAChB,mEAAmE;YACnE,6DAA6D;YAC7D,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,4CAA4C;YACvE,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,GAAG,4CAA4C;SAChF,CAAC;QAEF,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAAC,GAAY;QAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE9C,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,GAAG;gBACX,aAAa;gBACb,cAAc;gBACd,sBAAsB;gBACtB,gBAAgB;gBAChB,4BAA4B;aAC7B,CAAC;YAEF,IAAI,GAAG,EAAE,CAAC;gBACR,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;YAED,IAAI,CAAC,WAAW,GAAG,IAAA,qBAAK,EAAC,WAAW,EAAE,IAAI,EAAE;gBAC1C,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,MAAM;aACd,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,WAAoB,KAAK;QACtD,IAAI,CAAC;YACH,kDAAkD;YAClD,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAExC,uDAAuD;YACvD,IAAI,CAAC,OAAO,GAAG,MAAM,qBAAQ,CAAC,MAAM,CAAC;gBACnC,OAAO,EAAE,QAAQ;gBACjB,QAAQ;gBACR,IAAI,EAAE;oBACJ,aAAa;oBACb,cAAc;oBACd,sBAAsB;oBACtB,wBAAwB;oBACxB,gBAAgB;oBAChB,4BAA4B;oBAC5B,uCAAuC;oBACvC,0CAA0C;oBAC1C,kCAAkC;oBAClC,yCAAyC;oBACzC,+CAA+C;oBAC/C,yBAAyB;oBACzB,sBAAsB;oBACtB,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;iBACjF;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;YAChE,OAAO,IAAI,CAAC,OAAO,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAAC,OAAgB;QAC3C,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;YACvC,oBAAoB;YACpB,YAAY,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;YAC1C,eAAe,EAAE,KAAK;YACtB,iBAAiB,EAAE,IAAI;YACvB,SAAS,EAAE,IAAI;YACf,cAAc,EAAE,OAAO;SACxB,CAAC,CAAC;QAEH,0CAA0C;QAC1C,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;YAChC,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE;gBAC5B,oBAAoB;gBACpB,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;oBACxB,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;gBAC9B,CAAC;gBACD,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;oBAC1B,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;gBAChC,CAAC;gBACD,gBAAgB;gBAChB,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAS,CAAC;oBAC3C,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,CAAC;gBAC7G,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B;QACtC,IAAI,CAAC;YACH,0CAA0C;YAC1C,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACjC,IAAA,qBAAK,EAAC,UAAU,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;gBACpE,oCAAoC;gBACpC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,4CAA4C;QAC9C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,CAAC;IACH,CAAC;CACF;AApKD,sDAoKC"}